{"name": "resume-lm", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_TLS_REJECT_UNAUTHORIZED=0 NODE_NO_WARNINGS=1 next dev --turbopack", "dev:fast": "NODE_TLS_REJECT_UNAUTHORIZED=0 NODE_NO_WARNINGS=1 ENABLE_HTTP_PROXY=false next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "test:proxy": "node proxy-test.js", "perf:check": "node -e \"console.log('Node.js version:', process.version); console.log('Memory usage:', process.memoryUsage());\""}, "dependencies": {"@ai-sdk/anthropic": "^1.0.6", "@ai-sdk/deepseek": "^0.0.4", "@ai-sdk/google": "^1.0.12", "@ai-sdk/google-vertex": "^2.0.12", "@ai-sdk/groq": "^1.1.9", "@ai-sdk/openai": "^1.0.12", "@openrouter/ai-sdk-provider": "^0.0.6", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-pdf/renderer": "^4.1.6", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.4.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.10", "@tanstack/react-virtual": "^3.11.2", "@tiptap/core": "^2.11.3", "@tiptap/extension-bold": "^2.11.0", "@tiptap/extension-character-count": "^2.11.3", "@tiptap/extension-document": "^2.11.0", "@tiptap/extension-history": "^2.11.0", "@tiptap/extension-italic": "^2.11.3", "@tiptap/extension-paragraph": "^2.11.0", "@tiptap/extension-strike": "^2.11.3", "@tiptap/extension-text": "^2.11.0", "@tiptap/extension-text-align": "^2.11.3", "@tiptap/extension-text-style": "^2.11.3", "@tiptap/extension-underline": "^2.11.3", "@tiptap/pm": "^2.11.0", "@tiptap/react": "^2.11.0", "@tiptap/starter-kit": "^2.11.0", "@upstash/redis": "^1.34.4", "@vercel/analytics": "^1.5.0", "ai": "^4.0.23", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.15.0", "html2canvas": "^1.4.1", "html2pdf": "^0.0.11", "https-proxy-agent": "^7.0.6", "html2pdf.js": "^0.10.2", "install": "^0.13.0", "katex": "^0.16.19", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "marked": "^15.0.4", "next": "15.1.3", "node-fetch": "^3.3.2", "openai": "^4.77.0", "openai-edge": "^1.2.2", "pdf-parse": "^1.1.1", "pdf.js-extract": "^0.2.1", "pdfjs-dist": "4.8.69", "react": "^19.0.0", "react-circular-progressbar": "^2.1.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-intersection-observer": "^9.15.0", "react-markdown": "^9.0.1", "react-pdf": "^9.2.1", "react-pdftotext": "^1.3.4", "react-resizable-panels": "^2.1.7", "react-scan": "^0.0.54", "react-stripe-js": "^1.1.5", "rehype-katex": "^7.0.1", "rehype-prism-plus": "^2.0.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-math": "^6.0.0", "remark-toc": "^9.0.0", "replace-special-characters": "^1.2.7", "rich-textarea": "^0.26.4", "sonner": "^1.7.1", "stripe": "^18.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tiptap-markdown": "^0.8.10", "use-stick-to-bottom": "1.0.30", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/mdx": "^15.3.3", "@shadcn/ui": "^0.0.4", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/typography": "^0.5.15", "@types/lodash": "^4.17.13", "@types/node": "^20.17.11", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "15.1.2", "gray-matter": "^4.0.3", "next-mdx-remote": "^5.0.0", "postcss": "^8.4.49", "remark-gfm": "^4.0.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}, "engines": {"node": ">=20.0.0"}}