/**
 * 启动性能测试脚本
 * 
 * 使用方法：
 * 1. 运行 pnpm dev
 * 2. 在另一个终端运行：node test-startup.js
 * 3. 检查输出结果
 */

console.log('🧪 启动性能测试...\n');

// 测试 1: 检查配置文件
console.log('1. 📋 检查 Next.js 配置...');
const fs = require('fs');
const path = require('path');

try {
  const configPath = path.join(__dirname, 'next.config.ts');
  const configContent = fs.readFileSync(configPath, 'utf8');
  
  console.log('   ✅ next.config.ts 读取成功');
  
  // 检查是否包含可能导致问题的配置
  if (configContent.includes('withMDX')) {
    console.log('   ⚠️  包含 MDX 配置，可能导致 turbopack 警告');
  } else {
    console.log('   ✅ 已移除 MDX 配置');
  }
  
  if (configContent.includes('turbo:')) {
    console.log('   ⚠️  包含 turbo 配置');
  } else {
    console.log('   ✅ 无 turbo 配置');
  }
  
} catch (error) {
  console.log('   ❌ 无法读取配置文件');
}

// 测试 2: 检查性能问题
console.log('\n2. 🐌 性能问题分析...');
console.log('   根据日志分析：');
console.log('   • 数据库查询：3934ms (主要瓶颈)');
console.log('   • 页面编译：3.8s (次要问题)');
console.log('   • 代理使用：可能增加延迟');

// 测试 3: 解决方案
console.log('\n3. 🚀 解决方案...');
console.log('   优先级 1: 数据库索引优化');
console.log('   • 运行 fix-database-performance.sql');
console.log('   • 预期改善：3934ms → 100-500ms');
console.log('');
console.log('   优先级 2: 配置警告修复');
console.log('   • 已简化 next.config.ts');
console.log('   • 移除了 MDX 配置');
console.log('');
console.log('   优先级 3: 代理优化');
console.log('   • 考虑禁用开发环境代理');
console.log('   • 或使用更快的代理服务器');

// 测试 4: 下一步行动
console.log('\n4. 📋 立即行动清单...');
console.log('   [ ] 1. 测试当前配置是否消除警告');
console.log('   [ ] 2. 在 Supabase 中运行数据库索引脚本');
console.log('   [ ] 3. 重新测试页面加载时间');
console.log('   [ ] 4. 监控性能改善效果');

console.log('\n✅ 测试完成！');
console.log('💡 现在请运行 pnpm dev 检查是否还有配置警告');
