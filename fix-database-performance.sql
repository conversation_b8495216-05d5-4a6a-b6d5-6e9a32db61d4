-- ============================================================================
-- 数据库性能优化 - 解决 3.9秒 查询问题
-- ============================================================================
-- 
-- 问题：数据库查询耗时 3934ms，这是性能瓶颈的主要原因
-- 解决：添加关键索引来加速查询
--
-- 使用方法：
-- 1. 打开 Supabase SQL 编辑器
-- 2. 复制粘贴下面的 SQL 语句
-- 3. 执行脚本
-- 4. 预期查询时间从 3.9秒 降低到 100-500ms
-- ============================================================================

-- 检查当前索引状态
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('resumes', 'profiles')
ORDER BY tablename, indexname;

-- 为 resumes 表添加关键索引
-- 这个索引将大幅提升 getResumeById 查询性能
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resumes_user_id_id 
ON resumes(user_id, id);

-- 为 resumes 表添加用户查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_resumes_user_id 
ON resumes(user_id);

-- 为 profiles 表添加用户查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_profiles_user_id 
ON profiles(user_id);

-- 为 jobs 表添加索引（如果需要）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_jobs_user_id 
ON jobs(user_id);

-- 更新表统计信息
ANALYZE resumes;
ANALYZE profiles;
ANALYZE jobs;

-- 验证索引创建成功
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN ('resumes', 'profiles', 'jobs')
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- ============================================================================
-- 性能测试查询
-- ============================================================================
-- 
-- 执行完索引后，可以用这些查询测试性能：

-- 测试 resume 查询性能（替换为实际的 ID）
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM resumes 
WHERE id = 'b7f470d0-91f1-4a30-882c-1f0a2137f234' 
AND user_id = 'fbcfdbdf-019e-47ad-9274-f0e31b2c692d';

-- 测试 profile 查询性能（替换为实际的 user_id）
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM profiles 
WHERE user_id = 'fbcfdbdf-019e-47ad-9274-f0e31b2c692d';

-- ============================================================================
-- 预期结果
-- ============================================================================
--
-- 添加索引后，预期性能改善：
-- - 数据库查询时间：3934ms → 100-500ms (80-95% 改善)
-- - 总 API 响应时间：8173ms → 2-4秒 (50-75% 改善)
-- - 页面加载体验：显著提升
--
-- ============================================================================
