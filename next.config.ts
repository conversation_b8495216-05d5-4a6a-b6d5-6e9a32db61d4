/* eslint-disable import/no-commonjs */
/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { NextConfig } from 'next'
import remarkGfm from 'remark-gfm'

// eslint-disable-next-line import/no-extraneous-dependencies
import mdx from '@next/mdx'

const withMDX = mdx({
  extension: /\.mdx?$/,
  options: {
    remarkPlugins: [remarkGfm],
    rehypePlugins: [],
  },
});

const nextConfig: NextConfig = {
  // Turbopack configuration for Next.js 15
  experimental: {
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    // Enable performance optimizations
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },

  // External packages for server components
  serverExternalPackages: ['@react-pdf/renderer', 'canvas', 'jsdom'],

  // Allow MDX files to be considered pages/components
  pageExtensions: ['ts', 'tsx', 'mdx'],
  productionBrowserSourceMaps: false,
  reactStrictMode: false,

  // Webpack optimizations for fallback
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        canvas: false,
        encoding: false,
      };
    }
    return config;
  },
}

export default withMDX(nextConfig)