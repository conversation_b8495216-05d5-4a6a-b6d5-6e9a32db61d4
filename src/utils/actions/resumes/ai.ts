'use server';

// import { RESUME_IMPORTER_SYSTEM_MESSAGE, } from "@/lib/prompts";
import { Resume } from "@/lib/types";
import { textImportSchema, workExperienceBulletPointsSchema } from "@/lib/zod-schemas";
import { generateObject } from "ai";
import { z } from "zod";
import { initializeAIClient, type AIConfig } from '@/utils/ai-tools';
import { getSubscriptionPlan } from "@/utils/actions/stripe/actions";
import { PROJECT_GENERATOR_MESSAGE, PROJECT_IMPROVER_MESSAGE, TEXT_ANALYZER_SYSTEM_MESSAGE, WORK_EXPERIENCE_GENERATOR_MESSAGE, WORK_EXPERIENCE_IMPROVER_MESSAGE } from "@/lib/prompts";
import { projectAnalysisSchema, workExperienceItemsSchema } from "@/lib/zod-schemas";
import { WorkExperience } from "@/lib/types";



// Base Resume Creation 
// TEXT CONTENT -> RESUME
export async function convertTextToResume(prompt: string, existingResume: Resume, targetRole: string, config?: AIConfig) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _unusedConfig = config; // Keep parameter for future use
  
  // Hardcode to use gpt-4.1-nano for now
  const hardcodedConfig = { model: 'gpt-4.1-nano', apiKeys: [] };
  const aiClient = initializeAIClient(hardcodedConfig);

  
  const { object } = await generateObject({
    model: aiClient,
    schema: z.object({
      content: textImportSchema
    }),
    system: `您是简历格式化器，一个专门分析完整简历并将其转换为结构化JSON对象的专家系统。

        您的任务是将完整简历文本转换为JSON对象，根据提供的模式。您将识别并提取与目标角色最相关的经验、技能、项目和教育背景。在执行此操作时，您允许进行最小格式化更改，以提高清晰度和强调相关性——**不要重新编写、总结或更改任何内容的实质性细节。**

        关键指令：
        1. **分析与选择：**
          - 分析包含所有用户经验、技能、项目和教育信息的完整简历文本。
          - 确定与目标角色最匹配的条目：${targetRole}。
          - 始终包含教育部分：
            - 如果只有一个教育条目，则包含它。
            - 如果存在多个条目，请选择与目标角色最相关的条目。

        2. **格式化与强调：**
          - 将简历转换为JSON对象，遵循模式，包括基本信息、专业经验、项目、技能和教育。
          - 保留所有原始细节、日期和描述。仅对文本进行格式化。
          - **通过加粗格式化增强相关性**，在工作经验描述、项目详情、成就和教育详情中标记与目标角色高度相关的关键词或短语（例如，用两个星号包裹它们，如**此**）。仅对关键词或短语应用此格式，这些关键词或短语与目标角色高度相关。
          - 不要为章节标题或标题添加任何格式。
          - 对于不包含相关条目的任何部分，请使用空数组（[]）。

        3. **输出要求：**
          - 最终输出必须是一个有效的JSON对象，符合指定的模式。

          - 仅包含最相关的条目，优化为目标角色。
          - 不要添加任何新信息或重新表述所提供的内容——仅对关键点应用最小格式化（如加粗）。         
          - 重要提示：请以用户信息相同的语言响应。如果输入为中文，输出必须为中文。如果输入为英文，输出必须为英文。
        `,
    prompt: `输入：
    从以下文本中提取并转换简历信息：
    ${prompt}
    现在，根据模式格式化此信息，确保其优化为目标角色：${targetRole}。`,
    
  });
  
  const updatedResume = {
    ...existingResume,
    ...(object.content.first_name && { first_name: object.content.first_name }),
    ...(object.content.last_name && { last_name: object.content.last_name }),
    ...(object.content.email && { email: object.content.email }),
    ...(object.content.phone_number && { phone_number: object.content.phone_number }),
    ...(object.content.location && { location: object.content.location }),
    ...(object.content.website && { website: object.content.website }),
    ...(object.content.linkedin_url && { linkedin_url: object.content.linkedin_url }),
    ...(object.content.github_url && { github_url: object.content.github_url }),
    
    work_experience: [...existingResume.work_experience, ...(object.content.work_experience || [])],
    education: [...existingResume.education, ...(object.content.education || [])],
    skills: [...existingResume.skills, ...(object.content.skills || [])],
    projects: [...existingResume.projects, ...(object.content.projects || [])],
  };

  
  return updatedResume;
}



    // NEW WORK EXPERIENCE BULLET POINTS
    export async function generateWorkExperiencePoints(
      position: string,
      company: string,
      technologies: string[],
      targetRole: string,
      numPoints: number = 3,
      customPrompt: string = '',
      config?: AIConfig
    ) { 
      const subscriptionPlan = await getSubscriptionPlan();
      const isPro = subscriptionPlan === 'pro';
      const aiClient = isPro ? initializeAIClient(config, isPro) : initializeAIClient(config);
  
      const { object } = await generateObject({
        model: aiClient,
        schema: z.object({
          content: workExperienceBulletPointsSchema
        }),
      prompt: `职位：${position}
      公司：${company}
      技术：${technologies.join(', ')}
      目标角色：${targetRole}
      点数：${numPoints}${customPrompt ? `\n自定义重点：${customPrompt}` : ''}`,
        system: WORK_EXPERIENCE_GENERATOR_MESSAGE.content as string,
      });

      return object.content;
      }
    
      // WORK EXPERIENCE BULLET POINTS IMPROVEMENT
      export async function improveWorkExperience(point: string, customPrompt?: string, config?: AIConfig) {
          const subscriptionPlan = await getSubscriptionPlan();
          const isPro = subscriptionPlan === 'pro';
          const aiClient = isPro ? initializeAIClient(config, isPro) : initializeAIClient(config);
          
          const { object } = await generateObject({
          model: aiClient,
          
          schema: z.object({
              content: z.string().describe("The improved work experience bullet point")
          }),
          prompt: `请改进此工作经历要点，同时保持其核心信息和真实性${customPrompt ? `. 额外要求：${customPrompt}` : ''}：\n\n"${point}"`,
          system: WORK_EXPERIENCE_IMPROVER_MESSAGE.content as string,
          });
      

          return object.content;
      }
    
      // PROJECT BULLET POINTS IMPROVEMENT
      export async function improveProject(point: string, customPrompt?: string, config?: AIConfig) {
          
          const subscriptionPlan = await getSubscriptionPlan();
          const isPro = subscriptionPlan === 'pro';
          const aiClient = isPro ? initializeAIClient(config, isPro) : initializeAIClient(config);

  
          const { object } = await generateObject({
          model: aiClient,
          schema: z.object({
              content: z.string().describe("The improved project bullet point")
          }),
          prompt: `请改进此项目要点，同时保持其核心信息和真实性${customPrompt ? `. 额外要求：${customPrompt}` : ''}：\n\n"${point}"`,
          system: PROJECT_IMPROVER_MESSAGE.content as string,
          });
      
          return object.content;
      }
      
      // NEW PROJECT BULLET POINTS
      export async function generateProjectPoints(
          projectName: string,
          technologies: string[],
          targetRole: string,
          numPoints: number = 3,
          customPrompt: string = '',
          config?: AIConfig
      ) {
          const subscriptionPlan = await getSubscriptionPlan();
          const isPro = subscriptionPlan === 'pro';
          const aiClient = isPro ? initializeAIClient(config, isPro) : initializeAIClient(config);
          
          const { object } = await generateObject({
          model: aiClient,
          schema: z.object({
              content: projectAnalysisSchema
          }),
          prompt: `项目名称：${projectName}
      技术：${technologies.join(', ')}
      目标角色：${targetRole}
      点数：${numPoints}${customPrompt ? `\n自定义重点：${customPrompt}` : ''}`,
          system: PROJECT_GENERATOR_MESSAGE.content as string,
          });
      
          return object.content;
      }
      
      // Text Import for profile
      export async function processTextImport(text: string, config?: AIConfig) {
          const aiClient = initializeAIClient(config);
          
          const { object } = await generateObject({
          model: aiClient,
          schema: z.object({
              content: textImportSchema
          }),
          prompt: text,
          system: TEXT_ANALYZER_SYSTEM_MESSAGE.content as string,
          });
      
          return object.content;
      }
      
      // WORK EXPERIENCE MODIFICATION
      export async function modifyWorkExperience(
          experience: WorkExperience[],
          prompt: string,
          config?: AIConfig
      ) {
          const subscriptionPlan = await getSubscriptionPlan();
          const isPro = subscriptionPlan === 'pro';
          const aiClient = isPro ? initializeAIClient(config, isPro) : initializeAIClient(config);
          
          const { object } = await generateObject({
          model: aiClient,
          schema: z.object({
              content: workExperienceItemsSchema
          }),
          prompt: `请根据以下指示修改此工作经历条目：${prompt}\n\n当前工作经历：\n${JSON.stringify(experience, null, 2)}`,
          system: `您是专业简历编写者。根据用户指示修改给定工作经历。 
          保持专业性和准确性，同时实施请求的更改。
          保持相同的职位和日期，但根据请求修改其他字段。
          在可能的情况下使用强动词和可量化的成就。         
          重要提示：请以用户信息相同的语言响应。如果输入为中文，输出必须为中文。如果输入为英文，输出必须为英文。`,
          });
      
          return object.content;
      }
      
      // ADDING TEXT CONTENT TO RESUME
      export async function addTextToResume(prompt: string, existingResume: Resume, config?: AIConfig) {
          const subscriptionPlan = await getSubscriptionPlan();
          const isPro = subscriptionPlan === 'pro';
          const aiClient = isPro ? initializeAIClient(config, isPro) : initializeAIClient(config);
  
          
          const { object } = await generateObject({
          model: aiClient,
          schema: z.object({
              content: textImportSchema
          }),
          prompt: `从以下文本中提取相关简历信息，包括基本信息（姓名、联系方式等）和专业经验。根据模式格式化：\n\n${prompt}`,
          system: TEXT_ANALYZER_SYSTEM_MESSAGE.content as string,
          });
          
          // Merge the AI-generated content with existing resume data
          const updatedResume = {
          ...existingResume,
          ...(object.content.first_name && { first_name: object.content.first_name }),
          ...(object.content.last_name && { last_name: object.content.last_name }),
          ...(object.content.email && { email: object.content.email }),
          ...(object.content.phone_number && { phone_number: object.content.phone_number }),
          ...(object.content.location && { location: object.content.location }),
          ...(object.content.website && { website: object.content.website }),
          ...(object.content.linkedin_url && { linkedin_url: object.content.linkedin_url }),
          ...(object.content.github_url && { github_url: object.content.github_url }),
          
          work_experience: [...existingResume.work_experience, ...(object.content.work_experience || [])],
          education: [...existingResume.education, ...(object.content.education || [])],
          skills: [...existingResume.skills, ...(object.content.skills || [])],
          projects: [...existingResume.projects, ...(object.content.projects || [])],
          };
          
          return updatedResume;
      }