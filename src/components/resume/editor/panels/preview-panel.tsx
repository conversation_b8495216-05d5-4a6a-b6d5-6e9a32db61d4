'use client';

import { Resume } from "@/lib/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { ResumePreview } from "../preview/resume-preview";
import CoverLetter from "@/components/cover-letter/cover-letter";
import { ResumeContextMenu } from "../preview/resume-context-menu";

interface PreviewPanelProps {
  resume: Resume;
  onResumeChange: (field: keyof Resume, value: Resume[keyof Resume]) => void;
  width: number;
  // percentWidth: number;
}

export function PreviewPanel({
  resume,
  onResumeChange,
  width,
}: PreviewPanelProps) {
  return (
    <ScrollArea className="h-full">
      <div className="p-6 space-y-6">
        <ResumeContextMenu resume={resume}>
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <ResumePreview
              resume={resume}
              containerWidth={width}
            />
          </div>
        </ResumeContextMenu>

        {resume.has_cover_letter && (
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <CoverLetter
              resume={resume}
              onResumeChange={onResumeChange}
            />
          </div>
        )}
      </div>
    </ScrollArea>
  );
}