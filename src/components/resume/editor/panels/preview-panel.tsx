'use client';

import { Resume } from "@/lib/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import dynamic from 'next/dynamic';

// 动态导入重型 PDF 组件
const ResumePreview = dynamic(() => import("../preview/resume-preview").then(mod => ({ default: mod.ResumePreview })), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full bg-gray-50 rounded-lg">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
        <p className="text-lg font-medium text-gray-700">Generating PDF Preview...</p>
        <p className="text-sm text-gray-500 mt-1">This may take a moment</p>
      </div>
    </div>
  )
});

const CoverLetter = dynamic(() => import("@/components/cover-letter/cover-letter"), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-gray-200 h-96 rounded-lg"></div>
});

const ResumeContextMenu = dynamic(() => import("../preview/resume-context-menu").then(mod => ({ default: mod.ResumeContextMenu })), {
  ssr: false
});

interface PreviewPanelProps {
  resume: Resume;
  onResumeChange: (field: keyof Resume, value: Resume[keyof Resume]) => void;
  width: number;
  // percentWidth: number;
}

export function PreviewPanel({
  resume,
  // onResumeChange,
  width
}: PreviewPanelProps) {
  // 快速开发模式 - 禁用 PDF 预览以提高性能
  const isDevMode = process.env.NEXT_PUBLIC_DISABLE_PDF_PREVIEW === 'true';

  if (isDevMode) {
    return (
      <ScrollArea className="h-full">
        <div className="p-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-8 text-center">
            <div className="text-6xl mb-4">📄</div>
            <h3 className="text-xl font-semibold text-blue-800 mb-2">Fast Development Mode</h3>
            <p className="text-blue-700 mb-4">
              PDF preview is disabled for faster compilation and development.
            </p>
            <div className="bg-white rounded-lg p-4 text-left">
              <h4 className="font-medium text-gray-800 mb-2">Resume Data:</h4>
              <p className="text-sm text-gray-600">Name: {resume.first_name} {resume.last_name}</p>
              <p className="text-sm text-gray-600">Role: {resume.target_role}</p>
              <p className="text-sm text-gray-600">Experience: {resume.work_experience?.length || 0} items</p>
              <p className="text-sm text-gray-600">Projects: {resume.projects?.length || 0} items</p>
            </div>
            <p className="text-xs text-blue-500 mt-4">
              Remove NEXT_PUBLIC_DISABLE_PDF_PREVIEW from .env.local to enable full preview
            </p>
          </div>
        </div>
      </ScrollArea>
    );
  }

  return (
    <ScrollArea className={cn(
      "h-full",
      resume.is_base_resume
        ? "bg-purple-50/30"
        : "bg-pink-50/60 shadow-sm shadow-pink-200/20"
    )}>
      <div className="p-4">
        <ResumeContextMenu resume={resume}>
          <ResumePreview resume={resume} containerWidth={width} />
        </ResumeContextMenu>
      </div>

      <CoverLetter
        containerWidth={width}
      />
    </ScrollArea>
  );
}